<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"/>
  <title>黑马商城</title>

  <link rel="stylesheet" type="text/css" href="css/webbase2.css"/>
  <link rel="stylesheet" type="text/css" href="css/pages-index.css"/>

  <style type="text/css">
      .carousel-inner, .carousel-inner div {
          z-index: 9;
      }
  </style>
</head>

<body>
<div id="indexApp">
  <!-- 头部栏位 -->
  <div id="nav-bottom">
    <top></top>
    <div class='header' id='headApp'>
      <div class='py-container'>
        <div class='yui3-g Logo'>
          <div class='yui3-u Left logoArea'>
            <a href="/">
              <div class="logo">
                <img src="./img/logo.png" alt="1"/>
              </div>
            </a>
          </div>
          <div class='yui3-u Center searchArea'>
            <div class='search' style="font-weight:bold">
              <div action='' class='sui-form form-inline'>
                <!--searchAutoComplete-->
                <div class='input-append'>
                  <input style="font-weight:bold" type='text' id='autocomplete' v-model='key' @keyup="handleKey($event)"
                         @click.stop="" autocomplete="off" class='input-error input-xxlarge'
                         @focus="showOption=true&&options.length>0"/>
                  <button @click='search' class='sui-btn btn-xlarge btn-danger' type='button'>搜索</button>
                </div>
                <div v-show="showOption"
                     style="position: absolute;z-index:9999;border: 1px lightgrey solid;overflow-y:auto; height: 150px">
                  <ul style="list-style: none;margin-top: 0; background-color: #fff;width: 500px;">
                    <li :style="{cursor: 'pointer',width: '500px','background-color':index===i ? '#ddd':'#fff'}"
                        v-for="(option, i) in options" @mouseover="index=i"
                        @click.self.prevent="selectOption(option)">
                      {{option}}
                    </li>
                  </ul>
                </div>
              </div>

            </div>
            <div class='hotwords'>
              <ul>
                <li class='f-item'>黑马首发</li>
                <li class='f-item'>亿元优惠</li>
                <li class='f-item'>9.9元团购</li>
                <li class='f-item'>每满99减30</li>
                <li class='f-item'>亿元优惠</li>
                <li class='f-item'>9.9元团购</li>
                <li class='f-item'>办公用品</li>
              </ul>
            </div>
          </div>
          <div class='yui3-u Right shopArea'>
            <div class='fr shopcar'>
              <div class='show-shopcar' id='shopcar'>
                <span class='car'></span>
                <a class='sui-btn btn-default btn-xlarge' href='/cart.html'>
                  <span>我的购物车</span>
                  <i class='shopnum'>{{carts.length}}</i>
                </a>
                <div class='clearfix shopcarlist' id='shopcarlist' style='display:none'>
                  <p>'啊哦，你的购物车还没有商品哦！'</p>
                  <p>'啊哦，你的购物车还没有商品哦！'</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class='yui3-g '>
          <div class='yui3-u Left all-sort'>
            <h4></h4>
          </div>
          <div class='yui3-u Center navArea' style="z-index: 50">
            <ul class='nav'>
              <li class='f-item'>服装城</li>
              <li class='f-item'>美妆馆</li>
              <li class='f-item'>黑马超市</li>
              <li class='f-item'>全球购</li>
              <li class='f-item'>闪购</li>
              <li class='f-item'>团购</li>
              <li class='f-item'>有趣</li>
              <li class='f-item'><a href='/seckill-index.html' target='_blank'>秒杀</a></li>
            </ul>
          </div>
          <div class='yui3-u Right'></div>
        </div>
      </div>
    </div>
  </div>
  <!--分类列表-->
  <div>
    <div class="sort">
      <div class="py-container">
        <div class="yui3-g SortList ">
          <div class="yui3-u Left all-sort-list">
            <div class="all-sort-list2">
              <div class="item bo">
                <h3><a href="">图书、音像、数字商品</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书</a></dt>
                      <dd><a href="">免费</a><em><a href="">小说</a></em><a href="">励志与成功</a><em><a
                          href="">婚恋/两性</a></em><em><a
                          href="">文学</a></em><em><a href="">经管</a></em><em><a href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">家用电器</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书1</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                    <dl class="fore9">
                      <dt>少儿</dt>
                      <dd><em><a href="">少儿</a></em><em><a href="">0-2岁</a></em><em><a href="">3-6岁</a></em><em><a
                          href="">7-10岁</a></em><em><a href="">11-14岁</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">手机、数码</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书2</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">电脑、办公</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书3</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                    <dl class="fore9">
                      <dt>少儿</dt>
                      <dd><em><a href="">少儿</a></em><em><a href="">0-2岁</a></em><em><a href="">3-6岁</a></em><em><a
                          href="">7-10岁</a></em><em><a href="">11-14岁</a></em></dd>
                    </dl>
                    <dl class="fore10">
                      <dt>教育</dt>
                      <dd><em><a href="">教材教辅</a></em><em><a href="">考试</a></em><em><a href="">外语学习</a></em></dd>
                    </dl>
                    <dl class="fore11">
                      <dt>其它</dt>
                      <dd><em><a href="">英文原版书</a></em><em><a href="">港台图书</a></em><em><a href="">工具书</a></em><em><a
                          href="">套装书</a></em><em><a href="">杂志/期刊</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">家居、家具、家装、厨具</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书4</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                    <dl class="fore9">
                      <dt>少儿</dt>
                      <dd><em><a href="">少儿</a></em><em><a href="">0-2岁</a></em><em><a href="">3-6岁</a></em><em><a
                          href="">7-10岁</a></em><em><a href="">11-14岁</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">服饰内衣</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书5</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">个护化妆</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书6</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                    <dl class="fore9">
                      <dt>少儿</dt>
                      <dd><em><a href="">少儿</a></em><em><a href="">0-2岁</a></em><em><a href="">3-6岁</a></em><em><a
                          href="">7-10岁</a></em><em><a href="">11-14岁</a></em></dd>
                    </dl>
                    <dl class="fore10">
                      <dt>教育</dt>
                      <dd><em><a href="">教材教辅</a></em><em><a href="">考试</a></em><em><a href="">外语学习</a></em></dd>
                    </dl>
                    <dl class="fore11">
                      <dt>其它</dt>
                      <dd><em><a href="">英文原版书</a></em><em><a href="">港台图书</a></em><em><a href="">工具书</a></em><em><a
                          href="">套装书</a></em><em><a href="">杂志/期刊</a></em></dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">运动健康</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书7</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                  </div>
                  <div class="cat-right">
                    <dl class="categorys-brands" clstag="homepage|keycount|home2013|0601d">
                      <dt>推荐品牌出版商</dt>
                      <dd>
                        <ul>
                          <li>
                            <a href="">中华书局</a>
                          </li>
                          <li>
                            <a href="">人民邮电出版社</a>
                          </li>
                        </ul>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">汽车用品</a></h3>
                <div class="item-list clearfix">
                  <div class="subitem">
                    <dl class="fore1">
                      <dt><a href="">电子书8</a></dt>
                      <dd><em><a href="">免费</a></em><em><a href="">小说</a></em><em><a href="">励志与成功</a></em><em><a
                          href="">婚恋/两性</a></em><em><a href="">文学</a></em><em><a href="">经管</a></em><em><a
                          href="">畅读VIP</a></em></dd>
                    </dl>
                    <dl class="fore2">
                      <dt><a href="">数字音乐</a></dt>
                      <dd><em><a href="">通俗流行</a></em><em><a href="">古典音乐</a></em><em><a href="">摇滚说唱</a></em><em><a
                          href="">爵士蓝调</a></em><em><a href="">乡村民谣</a></em><em><a href="">有声读物</a></em></dd>
                    </dl>
                    <dl class="fore3">
                      <dt><a href="">音像</a></dt>
                      <dd><em><a href="">音乐</a></em><em><a href="">影视</a></em><em><a href="">教育音像</a></em><em><a
                          href="">游戏</a></em></dd>
                    </dl>
                    <dl class="fore4">
                      <dt>文艺</dt>
                      <dd><em><a href="">小说</a></em><em><a href="">文学</a></em><em><a href="">青春文学</a></em><em><a
                          href="">传记</a></em><em><a href="">艺术</a></em></dd>
                    </dl>
                    <dl class="fore5">
                      <dt>人文社科</dt>
                      <dd><em><a href="">历史</a></em><em><a href="">心理学</a></em><em><a href="">政治/军事</a></em><em><a
                          href="">国学/古籍</a></em><em><a href="">哲学/宗教</a></em><em><a href="">社会科学</a></em></dd>
                    </dl>
                    <dl class="fore6">
                      <dt>经管励志</dt>
                      <dd><em><a href="">经济</a></em><em><a href="">金融与投资</a></em><em><a href="">管理</a></em><em><a
                          href="">励志与成功</a></em></dd>
                    </dl>
                    <dl class="fore7">
                      <dt>生活</dt>
                      <dd><em><a href="">家庭与育儿</a></em><em><a href="">旅游/地图</a></em><em><a href="">烹饪/美食</a></em><em><a
                          href="">时尚/美妆</a></em><em><a href="">家居</a></em><em><a href="">婚恋与两性</a></em><em><a href="">娱乐/休闲</a></em><em><a
                          href="">健身与保健</a></em><em><a href="">动漫/幽默</a></em><em><a href="">体育/运动</a></em></dd>
                    </dl>
                    <dl class="fore8">
                      <dt>科技</dt>
                      <dd><em><a href="">科普</a></em><em><a href="">IT</a></em><em><a href="">建筑</a></em><em><a href="">医学</a></em><em><a
                          href="">工业技术</a></em><em><a href="">电子/通信</a></em><em><a href="">农林</a></em><em><a href="">科学与自然</a></em>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="item">
                <h3><a href="">彩票、旅行</a></h3>
              </div>
              <div class="item">
                <h3><a href="">理财、众筹</a></h3>
              </div>
              <div class="item">
                <h3><a href="">母婴、玩具</a></h3>
              </div>
              <div class="item">
                <h3><a href="">箱包</a></h3>
              </div>
              <div class="item">
                <h3><a href="">运动户外</a></h3>
              </div>
              <div class="item">
                <h3><a href="">箱包</a></h3>
              </div>
            </div>
          </div>
          <div class="yui3-u Center banerArea">
            <!--banner轮播-->
            <div id="myCarousel" data-ride="carousel" data-interval="4000" class="sui-carousel slide">
              <ol class="carousel-indicators">
                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                <li data-target="#myCarousel" data-slide-to="1"></li>
                <li data-target="#myCarousel" data-slide-to="2"></li>
              </ol>
              <div class="carousel-inner">
                <div class="active item">
                  <a href="/">
                    <img src="img/banner1.png"/>
                  </a>
                </div>
                <div class="item">
                  <a href="/">
                    <img src="img/banner2.jpg"/>
                  </a>
                </div>
                <div class="item">
                  <a href="/">
                    <img src="img/banner2.jpg"/>
                  </a>

                </div>
              </div>
              <a href="#myCarousel" data-slide="prev" class="carousel-control left">‹</a><a href="#myCarousel"
                                                                                            data-slide="next"
                                                                                            class="carousel-control right">›</a>
            </div>
          </div>
          <div class="yui3-u Right">
            <div class="news">
              <h4><em class="fl">黑马快报</em><span class="fr tip">更多 ></span></h4>
              <div class="clearix"></div>
              <ul class="news-list unstyled">
                <li>
                  <span class="bold">[特惠]</span>备战开学季 全民半价购数码
                </li>
                <li>
                  <span class="bold">[公告]</span>备战开学季 全民半价购数码
                </li>
                <li>
                  <span class="bold">[特惠]</span>备战开学季 全民半价购数码
                </li>
                <li>
                  <span class="bold">[公告]</span>备战开学季 全民半价购数码
                </li>
                <li>
                  <span class="bold">[特惠]</span>备战开学季 全民半价购数码
                </li>
              </ul>
            </div>
            <ul class="yui3-g Lifeservice">
              <li class="yui3-u-1-4 life-item tab-item">
                <i class="list-item list-item-1"></i>
                <span class="service-intro">话费</span>
              </li>
              <li class="yui3-u-1-4 life-item tab-item">
                <i class="list-item list-item-2"></i>
                <span class="service-intro">机票</span>
              </li>
              <li class="yui3-u-1-4 life-item tab-item">
                <i class="list-item list-item-3"></i>
                <span class="service-intro">电影票</span>
              </li>
              <li class="yui3-u-1-4 life-item tab-item">
                <i class="list-item list-item-4"></i>
                <span class="service-intro">游戏</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-5"></i>
                <span class="service-intro">彩票</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-6"></i>
                <span class="service-intro">加油站</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-7"></i>
                <span class="service-intro">酒店</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-8"></i>
                <span class="service-intro">火车票</span>
              </li>
              <li class="yui3-u-1-4 life-item  notab-item">
                <i class="list-item list-item-9"></i>
                <span class="service-intro">众筹</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-10"></i>
                <span class="service-intro">理财</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-11"></i>
                <span class="service-intro">礼品卡</span>
              </li>
              <li class="yui3-u-1-4 life-item notab-item">
                <i class="list-item list-item-12"></i>
                <span class="service-intro">白条</span>
              </li>
            </ul>
            <div class="life-item-content">
              <div class="life-detail">
                <i class="close">关闭</i>
                <p>话费充值</p>
                <form action="" class="sui-form form-horizontal">
                  号码：<input type="text" id="inputphoneNumber" placeholder="输入你的号码"/>
                </form>
                <button class="sui-btn btn-danger">快速充值</button>
              </div>
              <div class="life-detail">
                <i class="close">关闭</i> 机票
              </div>
              <div class="life-detail">
                <i class="close">关闭</i> 电影票
              </div>
              <div class="life-detail">
                <i class="close">关闭</i> 游戏
              </div>
            </div>
            <div class="ads">
              <img src="img/ad1.png"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--推荐-->
  <div class="show">
    <div class="py-container">
      <ul class="yui3-g Recommend">
        <li class="yui3-u-1-6  clock">
          <div class="time">
            <img src="img/clock.png"/>
            <h3>今日推荐</h3>
          </div>
        </li>
        <li class="yui3-u-5-24">
          <a href="list.html" target="_blank"><img src="img/today01.png"/></a>
        </li>
        <li class="yui3-u-5-24">
          <img src="img/today02.png"/>
        </li>
        <li class="yui3-u-5-24">
          <img src="img/today03.png"/>
        </li>
        <li class="yui3-u-5-24">
          <img src="img/today04.png"/>
        </li>
      </ul>
    </div>
  </div>
  <!--喜欢-->
  <div class="like">
    <div class="py-container">
      <div class="title">
        <h3 class="fl">猜你喜欢</h3>
        <b class="border"></b>
        <a href="javascript:;" class="fr tip changeBnt" id="xxlChg"><i></i>换一换</a>
      </div>
      <div class="bd">
        <ul class="clearfix yui3-g Favourate picLB" id="picLBxxl">
          <li class="yui3-u-1-6">
            <dl class="picDl huozhe">
              <dd>
                <a href="" class="pic"><img src="img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>阳光美包新款单肩包女包时尚子母包四件套女</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
              <dd>
                <a href="" class="pic"><img src="img/like_01.png" alt=""/></a>
                <div class="like-text">
                  <p>爱仕达 30CM炒锅不粘锅NWG8330E电磁炉炒</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
            </dl>
          </li>
          <li class="yui3-u-1-6">
            <dl class="picDl jilu">
              <dd>
                <a href="" class="pic"><img src="img/like_03.png" alt=""/></a>
                <div class="like-text">
                  <p>爱仕达 30CM炒锅不粘锅NWG8330E电磁炉炒</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
              <dd>
                <a href="" class="pic"><img src="img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>阳光美包新款单肩包女包时尚子母包四件套女</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
            </dl>
          </li>
          <li class="yui3-u-1-6">
            <dl class="picDl tuhua">
              <dd>
                <a href="" class="pic"><img src="img/like_01.png" alt=""/></a>
                <div class="like-text">
                  <p>捷波朗 </p>
                  <p>（jabra）BOOSI劲步</p>
                  <h3>¥236.00</h3>
                </div>
              </dd>
              <dd>
                <a href="" class="pic"><img nsrc="assets/img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>三星（G5500）</p>
                  <p>移动联通双网通</p>
                  <h3>¥566.00</h3>
                </div>
              </dd>
            </dl>
          </li>
          <li class="yui3-u-1-6">
            <dl class="picDl huozhe">
              <dd>
                <a href="" class="pic"><img src="img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>阳光美包新款单肩包女包时尚子母包四件套女</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
              <dd>
                <a href="" class="pic"><img src="img/like_01.png" alt=""/></a>
                <div class="like-text">
                  <p>爱仕达 30CM炒锅不粘锅NWG8330E电磁炉炒</p>
                  <h3>¥116.00</h3>
                </div>
              </dd>
            </dl>
          </li>
          <li class="yui3-u-1-6">
            <dl class="picDl jilu">
              <dd>
                <a href="http://sc.chinaz.com/" class="pic"><img src="img/like_03.png" alt=""/></a>
                <div class="like-text">
                  <p>捷波朗 </p>
                  <p>（jabra）BOOSI劲步</p>
                  <h3>¥236.00</h3>
                </div>
              </dd>
              <dd>
                <a href="http://sc.chinaz.com/" class="pic"><img src="img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>欧普</p>
                  <p>JYLZ08面板灯平板灯铝</p>
                  <h3>¥456.00</h3>
                </div>
              </dd>
            </dl>
          </li>
          <li class="yui3-u-1-6">
            <dl class="picDl tuhua">
              <dd>
                <a href="http://sc.chinaz.com/" class="pic"><img src="img/like_01.png" alt=""/></a>
                <div class="like-text">
                  <p>三星（G5500）</p>
                  <p>移动联通双网通</p>
                  <h3>¥566.00</h3>
                </div>
              </dd>
              <dd>
                <a href="http://sc.chinaz.com/" class="pic"><img nsrc="assets/img/like_02.png" alt=""/></a>
                <div class="like-text">
                  <p>韩国所望紧致湿润精华露400ml</p>
                  <h3>¥896.00</h3>
                </div>
              </dd>
            </dl>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <!--楼层-->
  <div id="floor-1" class="floor">
    <div class="py-container">
      <div class="title floors">
        <h3 class="fl">家用电器</h3>
        <div class="fr">
          <ul class="sui-nav nav-tabs">
            <li class="active">
              <a href="#tab1" data-toggle="tab">热门</a>
            </li>
            <li>
              <a href="#tab2" data-toggle="tab">大家电</a>
            </li>
            <li>
              <a href="#tab3" data-toggle="tab">生活电器</a>
            </li>
            <li>
              <a href="#tab4" data-toggle="tab">厨房电器</a>
            </li>
            <li>
              <a href="#tab5" data-toggle="tab">应季电器</a>
            </li>
            <li>
              <a href="#tab6" data-toggle="tab">空气/净水</a>
            </li>
            <li>
              <a href="#tab7" data-toggle="tab">高端电器</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="clearfix  tab-content floor-content">
        <div id="tab1" class="tab-pane active">
          <div class="yui3-g Floor-1">
            <div class="yui3-u Left blockgary">
              <ul class="jd-list">
                <li>节能补贴</li>
                <li>4K电视</li>
                <li>空气净化器</li>
                <li>IH电饭煲</li>
                <li>滚筒洗衣机</li>
                <li>电热水器</li>
              </ul>
              <img src="img/floor-1-1.png"/>
            </div>
            <div class="yui3-u row-330 floorBanner">
              <div id="floorCarousel" data-ride="carousel" data-interval="4000" class="sui-carousel slide">
                <ol class="carousel-indicators">
                  <li data-target="#floorCarousel" data-slide-to="0" class="active"></li>
                  <li data-target="#floorCarousel" data-slide-to="1"></li>
                  <li data-target="#floorCarousel" data-slide-to="2"></li>
                </ol>
                <div class="carousel-inner">
                  <div class="active item">
                    <img src="img/floor-1-b01.png">
                  </div>
                  <div class="item">
                    <img src="img/floor-1-b02.png">
                  </div>
                  <div class="item">
                    <img src="img/floor-1-b03.png">
                  </div>
                </div>
                <a href="#floorCarousel" data-slide="prev" class="carousel-control left">‹</a>
                <a href="#floorCarousel" data-slide="next" class="carousel-control right">›</a>
              </div>
            </div>
            <div class="yui3-u row-220 split">
              <span class="floor-x-line"></span>
              <div class="floor-conver-pit">
                <img src="img/floor-1-2.png"/>
              </div>
              <div class="floor-conver-pit">
                <img src="img/floor-1-3.png"/>
              </div>
            </div>
            <div class="yui3-u row-218 split">
              <img src="img/floor-1-4.png"/>
            </div>
            <div class="yui3-u row-220 split">
              <span class="floor-x-line"></span>
              <div class="floor-conver-pit">
                <img src="img/floor-1-5.png"/>
              </div>
              <div class="floor-conver-pit">
                <img src="img/floor-1-6.png"/>
              </div>
            </div>
          </div>
        </div>
        <div id="tab2" class="tab-pane">
          <p>第二个</p>
        </div>
        <div id="tab3" class="tab-pane">
          <p>第三个</p>
        </div>
        <div id="tab4" class="tab-pane">
          <p>第4个</p>
        </div>
        <div id="tab5" class="tab-pane">
          <p>第5个</p>
        </div>
        <div id="tab6" class="tab-pane">
          <p>第6个</p>
        </div>
        <div id="tab7" class="tab-pane">
          <p>第7个</p>
        </div>
      </div>
    </div>
  </div>
  <div id="floor-2" class="floor">
    <div class="py-container">
      <div class="title floors">
        <h3 class="fl">手机通讯</h3>
        <div class="fr">
          <ul class="sui-nav nav-tabs">
            <li class="active">
              <a href="#tab8" data-toggle="tab">热门</a>
            </li>
            <li>
              <a href="#tab9" data-toggle="tab">品质优选</a>
            </li>
            <li>
              <a href="#tab10" data-toggle="tab">新机尝鲜</a>
            </li>
            <li>
              <a href="#tab11" data-toggle="tab">高性价比</a>
            </li>
            <li>
              <a href="#tab12" data-toggle="tab">合约机</a>
            </li>
            <li>
              <a href="#tab13" data-toggle="tab">手机卡</a>
            </li>
            <li>
              <a href="#tab14" data-toggle="tab">手机配件</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="clearfix  tab-content floor-content">
        <div id="tab8" class="tab-pane active">
          <div class="yui3-g Floor-1">
            <div class="yui3-u Left blockgary">
              <ul class="jd-list">
                <li>节能补贴</li>
                <li>4K电视</li>
                <li>空气净化器</li>
                <li>IH电饭煲</li>
                <li>滚筒洗衣机</li>
                <li>电热水器</li>
              </ul>
              <img src="img/floor-1-1.png"/>
            </div>
            <div class="yui3-u row-330 floorBanner">
              <div id="floorCarousell" data-ride="carousel" data-interval="4000" class="sui-carousel slide">
                <ol class="carousel-indicators">
                  <li data-target="#floorCarousell" data-slide-to="0" class="active"></li>
                  <li data-target="#floorCarousell" data-slide-to="1"></li>
                  <li data-target="#floorCarousell" data-slide-to="2"></li>
                </ol>
                <div class="carousel-inner">
                  <div class="active item">
                    <img src="img/floor-1-b01.png">
                  </div>
                  <div class="item">
                    <img src="img/floor-1-b02.png">
                  </div>
                  <div class="item">
                    <img src="img/floor-1-b03.png">
                  </div>
                </div>
                <a href="#floorCarousell" data-slide="prev" class="carousel-control left">‹</a>
                <a href="#floorCarousell" data-slide="next" class="carousel-control right">›</a>
              </div>
            </div>
            <div class="yui3-u row-220 split">
              <span class="floor-x-line"></span>
              <div class="floor-conver-pit">
                <img src="img/floor-1-2.png"/>
              </div>
              <div class="floor-conver-pit">
                <img src="img/floor-1-3.png"/>
              </div>
            </div>
            <div class="yui3-u row-218 split">
              <img src="img/floor-1-4.png"/>
            </div>
            <div class="yui3-u row-220 split">
              <span class="floor-x-line"></span>
              <div class="floor-conver-pit">
                <img src="img/floor-1-5.png"/>
              </div>
              <div class="floor-conver-pit">
                <img src="img/floor-1-6.png"/>
              </div>
            </div>
          </div>
        </div>
        <div id="tab15" class="tab-pane">
          <p>第二个</p>
        </div>
        <div id="tab9" class="tab-pane">
          <p>第三个</p>
        </div>
        <div id="tab10" class="tab-pane">
          <p>第4个</p>
        </div>
        <div id="tab11" class="tab-pane">
          <p>第5个</p>
        </div>
        <div id="tab12" class="tab-pane">
          <p>第6个</p>
        </div>
        <div id="tab13" class="tab-pane">
          <p>第7个</p>
        </div>
        <div id="tab14" class="tab-pane">
          <p>第8个</p>
        </div>
      </div>
    </div>
  </div>
  <!--商标-->
  <div class="brand">
    <div class="py-container">
      <ul class="Brand-list blockgary">
        <li class="Brand-item">
          <img src="img/brand_21.png"/>
        </li>
        <li class="Brand-item"><img src="img/brand_03.png"/></li>
        <li class="Brand-item"><img src="img/brand_05.png"/></li>
        <li class="Brand-item"><img src="img/brand_07.png"/></li>
        <li class="Brand-item"><img src="img/brand_09.png"/></li>
        <li class="Brand-item"><img src="img/brand_11.png"/></li>
        <li class="Brand-item"><img src="img/brand_13.png"/></li>
        <li class="Brand-item"><img src="img/brand_15.png"/></li>
        <li class="Brand-item"><img src="img/brand_17.png"/></li>
        <li class="Brand-item"><img src="img/brand_19.png"/></li>
      </ul>
    </div>
  </div>


  <foot></foot>
  <!--页面底部END-->

  <!-- 楼层位置 -->
  <div id="floor-index" class="floor-index">
    <ul>
      <li>
        <a class="num" href="javascript:;" style="display: none;">1F</a>
        <a class="word" href="javascript;;" style="display: block;">家用电器</a>
      </li>
      <li>
        <a class="num" href="javascript:;" style="display: none;">2F</a>
        <a class="word" href="javascript;;" style="display: block;">手机通讯</a>
      </li>
      <li>
        <a class="num" href="javascript:;" style="display: none;">3F</a>
        <a class="word" href="javascript;;" style="display: block;">电脑办公</a>
      </li>
      <li>
        <a class="num" href="javascript:;" style="display: none;">4F</a>
        <a class="word" href="javascript;;" style="display: block;">家居家具</a>
      </li>
      <li>
        <a class="num" href="javascript:;" style="display: none;">5F</a>
        <a class="word" href="javascript;;" style="display: block;">运动户外</a>
      </li>
    </ul>
  </div>


</div>

<!--页面顶部，由js动态加载-->
<script src="./js/vue.js"></script>
<script src="./js/axios.min.js"></script>
<script src="./js/common.js"></script>
<script src="./js/top.js"></script>
<script src="./js/copyright.js"></script>
<script src="./js/foot.js"></script>
<script type="text/javascript">
  var indexVm = new Vue({
    el: "#indexApp",
    data() {
      return {
        util,
        key: this.getUrlParam("key"), // 用户输入的关键字
        query: location.search,
        showOption: false,// 是否要展示提示框
        options: [], // 搜索框的自动补全提示内容
        index: -1,// 目前选中的提示框内容的角标
        carts: [], // 购物车数据
      }
    },
    watch: {
      index(val) {
        if (val !== -1) {
          this.key = this.options[val];
        }
      }
    },
    created() {
      util.store.set("return-url", location.href);
      if(this.util.isLogin()){
        axios.get("/carts")
          .then(resp => this.carts = resp)
          .catch(err => {
            console.log(err);
            this.carts = []
          })
      }
    },
    methods: {
      handleKey(e) {
        if (e.keyCode === 13) {
          // 回车键，搜索
          this.search();
        }
        /*if (e.keyCode === 8 || (48 <= e.keyCode && e.keyCode <= 57) || (65 <= e.keyCode && e.keyCode <= 90)) {
          // 字母、数字、backspace键，需要去查询数据
          this.getSuggestion();
        } else if (e.keyCode === 13) {
          // 回车键，搜索
          this.search();
        } else if (e.keyCode === 27) {
          // esc键，取消提示框展示内容
          this.showOption = false;
        } else if (e.keyCode === 38) {
          // up键，选中提示框中的上一个选项
          this.index > 0 ? this.index-- : this.index = this.options.length - 1
        } else if (e.keyCode === 40) {
          // down键，选中提示框中的下一个选项
          this.index < this.options.length - 1 ? this.index++ : this.index = 0
        }*/
      },
      getSuggestion() {
        axios.get("/search/suggestion?key=" + this.key)
          .then(resp => {
            // 保存数据
            this.options = resp.data;
            // 显示提示框
            this.showOption = resp.data && resp.data.length > 0;
          })
          .catch(e => {
            console.log(e);
            this.options = []
          })
      },
      search() {
        if (this.key !== "null") {
          window.location = '/search.html';
          return;
        }
        window.location = '/search.html?key=' + this.key;
      },
      getUrlParam: function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
          return decodeURI(r[2]);
        }
        return null;
      },
      selectOption(o) {
        // 关闭提示框
        this.showOption = false;
        // 清空提示框内容
        this.options = [];
        // 跳转页面
        window.location = '/search.html?key=' + o;
      }
    }
  });
</script>
</body>


</html>