.logoArea {
	height: 84px
}

.loginArea {
	height: 487px;
	background-color: #7e00ff
}

.copyright {
	text-align: center;
	line-height: 24px
}


.banner{
	width: 100%;
	display: flex;
	justify-content: space-between;
	padding-top: 10px;
}

.login {
	background: url(../img/loginbg.png);
	height: 487px
}

.loginform {
	width: 380px;
	background: #fff;
	float: right;
	top: 45px;
	position: relative;
	padding: 20px
}

img {
	display: block;
	margin: 0 auto
}

.sui-form {
	margin-top: 15px
}

span.loginname {
	background-image: url(../img/icons.png);
	background-position: -10px -201px
}

.sui-form .input-prepend {
	margin-bottom: 16px
}

.sui-nav.nav-tabs.tab-wraped>li {
	width: 50%
}

.sui-nav.nav-tabs.tab-wraped>li.active>a {
	padding-top: 0;
	border-top: 1px solid #28a3ef
}

.sui-nav.nav-tabs.tab-wraped>li>a {
	padding: 0
}

.sui-nav.nav-tabs.tab-wraped>li.active h3 {
	color: #c8111a
}

.sui-form .input-prepend .add-on {
	padding: 6px 5px;
	background-color: #cfcdcd;
	width: 25px;
	height: 23px;
	*float: left
}

.sui-form input[type=text], .sui-form input[type=password] {
	height: 23px
}

.sui-form input.span2 {
	width: 284px;
	*float: left
}

.loginpwd {
	background-image: url(../img/icons.png);
	background-position: -72px -201px;
	display: block;
	width: 25px;
	height: 23px
}

.setting {
	position: relative;
	margin: 0 0 25px
}

.setting .forget {
	position: absolute;
	right: 0
}

.btn-danger {
	background-color: #c81623;
	padding: 6px;
	border-radius: 0;
	font-size: 16px;
	font-family: 微软雅黑;
	word-spacing: 4px
}

.otherlogin {
	position: relative;
	margin-top: 30px
}

.otherlogin .register {
	position: absolute;
	top: 10px;
	right: 10px;
	font-size: 15px
}

.types ul li {
	list-style-type: none;
	display: inline-block;
	*display: inline;
	*zoom: 1;
	width: 40px;
	height: 55px
}

.copyright ul li {
	list-style-type: none;
	display: inline-block;
	*display: inline;
	*zoom: 1;
	border-right: 1px solid #e4e4e4;
	padding: 0 20px;
	margin: 15px 0
}

.copyright ul li:last-child {
	border-right: 0
}