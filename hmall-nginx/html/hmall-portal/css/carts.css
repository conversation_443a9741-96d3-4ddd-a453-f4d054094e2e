.container{
    width: 100%;
    height: 500px;
    background-color: #fff;
    /*border-bottom: #fff 3px solid;*/
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
}
.tab-wrapper{
    position: relative;
    width: 100%;
    height: 60px;
    border-bottom: #a4a3a3 3px solid;
    /*background-color: #df3033;*/
}
.tab-wrapper .tab-radio{
    display: none;
}
.tab-handler{
    position: relative;
    z-index: 2;
    display: block;
    float: left;
    height: 60px;
    padding: 0 40px;
    color: #717181;
    font-size: 16px;
    line-height: 60px;
    /*transition: .3s;*/
    transform: scale(.9);
}
.tab-selected{
    color: #717181;
    border-bottom: #df3033 3px solid;
    background-color: #fff0f0;
    transform: scale(1);
}
.tab-wrapper .tab-content{
    position: absolute;
    top: 60px;
    left: 0;
    padding: 0 30px;
    color: #999;
    font-size: 14px;
    line-height: 1.618em;
    background-color: #fff;
    transition: transform .5s, opacity .7s;
    transform: translateY(20px);
}