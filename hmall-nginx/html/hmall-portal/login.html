<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=7; IE=EDGE">
  <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7"/>
  <title>黑马商城--登录页面</title>

  <link rel="stylesheet" type="text/css" href="./css/index.css"/>
  <link rel="stylesheet" type="text/css" href="./css/webbase.css"/>
  <link rel="stylesheet" type="text/css" href="./css/pages-login.css"/>
</head>

<body>
<div class="login-box"  id="loginApp">
  <top></top>
  <!--head-->
  <div class="py-container logoArea">
    <div class="banner">
      <div class="logo">
        <img src="./img/logo.png" alt="1"/>
      </div>
    </div>
  </div>
  <!--loginArea-->
  <div class="loginArea">
    <div class="py-container login">
      <div class="loginform">
        <ul class="sui-nav nav-tabs tab-wraped">
          <li>
            <a href="#index" data-toggle="tab">
              <h3>扫描登录</h3>
            </a>
          </li>
          <li class="active">
            <a href="#profile" data-toggle="tab">
              <h3>账户登录</h3>
            </a>
          </li>
        </ul>
        <div class="tab-content tab-wraped">
          <div id="index" class="tab-pane">
            <p>二维码登录，暂为官网二维码</p>
            <img src="./img/wx_cz.jpg"/>
          </div>
          <div id="profile" class="tab-pane  active">
            <span v-text="msg"></span>
            <form class="sui-form">
              <div class="input-prepend"><span class="add-on loginname"></span>
                <input id="username" type="text" placeholder="邮箱/用户名/手机号" class="span2 input-xfat"
                       v-model="form.username">
              </div>
              <div class="input-prepend"><span class="add-on loginpwd"></span>
                <input id="password" type="password" placeholder="请输入密码" class="span2 input-xfat"
                       v-model="form.password">
              </div>
              <div class="setting">
                <label class="checkbox inline">
                  <input name="m1" type="checkbox" value="2" checked="">
                  记住我
                </label>
                <span class="forget">忘记密码？</span>
              </div>
              <div class="logined">
                <a class="sui-btn btn-block btn-xlarge btn-danger" @click="login"
                   href="javascript:void(0)">登&nbsp;&nbsp;录</a>
              </div>
            </form>
            <div class="otherlogin">
              <div class="types">
                <ul>
                  <li><img src="./img/qq.png" width="35px" height="35px"/></li>
                  <li><img src="./img/sina.png"/></li>
                  <li><img src="./img/ali.png"/></li>
                  <li><img src="./img/weixin.png"/></li>
                </ul>
              </div>
              <span class="register"><a href="./index.html" target="_blank">立即注册</a></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

</body>
<script src="./js/vue.js"></script>
<script src="./js/axios.min.js"></script>
<script src="./js/common.js"></script>
<script src="./js/top.js"></script>
<script type="text/javascript">
  var loginVm = new Vue({
    el: "#loginApp",
    data: {
      form: {
        username: '',
        password: ''
      },
      msg: '',
      util
    },
    methods: {
      login() {
        axios.post("/users/login", this.form)
          .then(({token, ... user}) => {
            sessionStorage.setItem("token", token)
            util.store.set("user-info", user)
            const url = util.store.get("return-url");
            window.location = url || '/';
          })
          .catch(() => this.msg = '用户名或密码错误')
      }
    }
  });
</script>
</html>