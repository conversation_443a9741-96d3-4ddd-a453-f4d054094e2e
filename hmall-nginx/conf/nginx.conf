
worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/json;

    sendfile        on;
    
    keepalive_timeout  65;

     server {
        listen       80;
        server_name  localhost;
        # 指定前端项目所在的位置
        location / {
            root html;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
    }
    server {
        listen       18080;
        server_name  localhost;
        # 指定前端项目所在的位置
        location / {
            root html/hmall-portal;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
        location /api {
            rewrite /api/(.*)  /$1 break;
            proxy_pass http://localhost:8080;
        }
    }
    server {
        listen       18081;
        server_name  localhost;
        # 指定前端项目所在的位置
        location / {
            root html/hmall-admin;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
        location /api {
            rewrite /api/(.*)  /$1 break;
            proxy_pass http://localhost:8080;
        }
    }
    server {
        listen       18082;
        server_name  localhost;
        # 指定前端项目所在的位置
        location / {
            root html/hm-refresh-admin;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }
        location /api {
             proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Real-PORT $remote_port;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            rewrite /api/(.*)  /$1 break;
            proxy_pass http://localhost:8080;
        }
    }
    # upstream backend {
    #     server 127.0.0.1:8081 max_fails=5 fail_timeout=10s weight=1;
    #     server 127.0.0.1:8082 max_fails=5 fail_timeout=10s weight=1;
    # }  
}
